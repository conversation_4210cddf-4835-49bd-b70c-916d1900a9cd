variable "lambda_function_name" {
  description = "Name of the Lambda function to monitor"
  type        = string
}

variable "sns_topic_name" {
  description = "Name of the SNS topic for alarm notifications"
  type        = string
}

variable "evaluation_periods" {
  description = "Number of periods to evaluate for the alarm"
  type        = number
  default     = 1
  validation {
    condition     = var.evaluation_periods >= 1 && var.evaluation_periods <= 10
    error_message = "evaluation_periods must be between 1 and 10."
  }
}

variable "tags" {
  description = "List of key/value pairs for tags."
  type        = map(string)
}
