# Smart Analytics Processors - CI/CD Pipeline Quick Start

This guide will help you get started with the CI/CD pipeline for the Smart Analytics Processors project.

## Overview

The pipeline follows a **build-once, deploy-many** pattern:

1. **This Repository**: Tests, builds, and publishes Lambda packages
2. **Deployment Repository**: Consumes packages and deploys infrastructure

## Getting Started

### 1. Repository Setup

The pipeline is already configured and ready to use. It will automatically:

- ✅ Validate code quality on all branches and MRs
- ✅ Run tests on all branches and MRs  
- ✅ Build packages on main branch and version tags
- ✅ Publish packages to GitLab Package Registry

### 2. Current Lambda Functions

The pipeline currently supports:
- `agent-event-processor` - Processes agent events for Redshift

### 3. Triggering the Pipeline

#### Automatic Triggers

- **Merge Requests**: Validation and testing only
- **Main Branch**: Full pipeline (validate → test → build → publish)
- **Version Tags**: Full pipeline with versioned packages

#### Local Development and Testing

```bash
# Test locally (from Lambda directory)
cd lambdas/agent-event-processor
python -m pytest tests/unit/ -v --cov=src

# Run quality checks locally
ruff check .
flake8 src/ tests/
mypy src/
bandit -r src/

# Test event processing (mock mode)
python test_all_event_types.py --mock
```

**Note**: The pipeline automatically handles all building and packaging. No manual build steps are required!

## Adding New Lambda Functions

### Step 1: Create Lambda Structure

```
lambdas/your-new-function/
├── src/
│   ├── __init__.py
│   └── lambda_function.py
├── tests/
│   ├── __init__.py
│   └── test_lambda_function.py
├── requirements.txt
├── requirements-dev.txt
└── pyproject.toml
```

### Step 2: Update Pipeline Configuration

Edit `.gitlab-ci.yml`:

```yaml
variables:
  LAMBDA_FUNCTIONS: "agent-event-processor your-new-function"
```

Add test job:

```yaml
test:your-new-function:
  <<: *test_template
  variables:
    LAMBDA_FUNCTION: "your-new-function"
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH
```

Add build job:

```yaml
build:your-new-function:
  <<: *build_template
  variables:
    LAMBDA_FUNCTION: "your-new-function"
  rules:
    - if: $CI_COMMIT_BRANCH
    - if: $CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/
```

Update publish dependencies:

```yaml
publish:
  needs:
    - build:agent-event-processor
    - build:your-new-function
```

### Step 3: Test Your Changes

1. Create a merge request
2. Verify validation and tests pass
3. Merge to main branch
4. Verify build and publish succeed

## Package Consumption

### For Deployment Pipelines

Set these variables in your deployment pipeline:

```yaml
variables:
  SMARTANALYTICS_PROCESSORS_PROJECT_ID: "444"
  SMARTANALYTICS_PROCESSORS_VERSION: "latest"
```

Download packages in your `before_script`:

```bash
# Download latest packages
PACKAGE_VERSION=${SMARTANALYTICS_PROCESSORS_VERSION:-latest}
FUNCTIONS=("agent-event-processor" "your-new-function")

for func in "${FUNCTIONS[@]}"; do
  curl --header "JOB-TOKEN: $CI_JOB_TOKEN" \
       --output "/tmp/lambda_packages/${func}.zip" \
       --create-dirs \
       "${CI_API_V4_URL}/projects/${SMARTANALYTICS_PROCESSORS_PROJECT_ID}/packages/generic/smartanalytics-processors/${PACKAGE_VERSION}/${func}.zip"
done
```

### Package Versions

- **latest**: Always points to the most recent main branch build
- **v1.0.0**: Specific version tags for production deployments

## Versioning and Releases

### Creating a Release

1. Update version in `pyproject.toml` files
2. Create and push a version tag:

```bash
git tag v1.0.0
git push origin v1.0.0
```

3. Pipeline will automatically build and publish versioned packages

### Version Strategy

- **v1.0.0**: Major release (breaking changes)
- **v1.1.0**: Minor release (new features)
- **v1.0.1**: Patch release (bug fixes)

## Monitoring and Troubleshooting

### Pipeline Status

Check pipeline status in GitLab:
- Project → CI/CD → Pipelines
- View job logs for detailed information

### Common Issues

#### Validation Failures
- **Ruff formatting**: Code style issues - pipeline will auto-fix many
- **Flake8 linting**: Fix code style issues locally
- **MyPy errors**: Add type hints or fix type issues
- **Bandit security**: Address security warnings

#### Test Failures
- **Coverage below threshold**: Add more tests (current threshold: 60%)
- **Test errors**: Fix failing tests locally first
- **Import errors**: Check dependencies in requirements files

#### Build Failures
- **Missing dependencies**: Update requirements.txt with pinned versions
- **Large package size**: Optimize dependencies (current: ~50MB is normal)
- **Permission errors**: Check GitLab CI/CD permissions and job tokens

### Artifacts and Reports

The pipeline generates:
- **Test Results**: JUnit XML for GitLab integration
- **Coverage Reports**: HTML and XML formats
- **Lambda Packages**: ZIP files ready for deployment
- **Package Manifest**: JSON with package metadata

## Best Practices

### Code Quality

1. **Use Ruff formatting**: Pipeline automatically formats code
2. **Add type hints**: MyPy enforces type checking (lenient config)
3. **Write tests**: Maintain 60%+ coverage (gradually improve to 80%+)
4. **Security scanning**: Address Bandit warnings

### Dependencies

1. **Pin versions**: Use exact versions in requirements.txt
2. **Separate concerns**: Production vs development dependencies
3. **Minimize size**: Only include necessary packages
4. **Security updates**: Regularly update dependencies

### Testing

1. **Unit tests**: Test individual functions (pytest framework)
2. **Integration tests**: Test with mocked AWS services (moto library)
3. **Coverage**: Aim for 60%+ test coverage (gradually improve)
4. **Fast tests**: Keep test execution time reasonable (current: ~2 seconds)

### Deployment

1. **Use versioned packages**: For production deployments
2. **Test in dev first**: Validate changes before production
3. **Monitor deployments**: Check CloudWatch logs and metrics
4. **Rollback plan**: Keep previous versions available

## Support

### Documentation

- [Project README](README.md)
- [Deployment Pipeline Example](deployment-pipeline.yml)
- [Lambda Function Example](lambdas/agent-event-processor/)

### Getting Help

1. Check pipeline logs in GitLab CI/CD → Pipelines
2. Review error messages and artifacts
3. Test locally using the commands shown above
4. Contact the Smart Analytics Team

## Next Steps

1. **Review existing Lambda**: Study `agent-event-processor` as an example
2. **Add your Lambda**: Follow the steps above
3. **Test thoroughly**: Use local scripts and MR pipeline
4. **Deploy safely**: Use versioned packages for production
5. **Monitor**: Set up CloudWatch alarms and notifications

---

**Happy coding!** 🚀
