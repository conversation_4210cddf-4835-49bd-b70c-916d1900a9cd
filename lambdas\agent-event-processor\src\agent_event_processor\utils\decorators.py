"""Centralized decorators for the agent event processor.

This module provides reusable decorators for method tracing, logging, and other
cross-cutting concerns.
"""

import functools
from collections.abc import Callable
from typing import Any


def capture_method(func: Callable) -> Callable:
    """Decorator for method capture/tracing.

    Currently a no-op but can be extended for distributed tracing,
    performance monitoring, or debugging.

    Args:
        func: The function to decorate

    Returns:
        The decorated function
    """

    @functools.wraps(func)
    def wrapper(*args: Any, **kwargs: Any) -> Any:
        # Future: Add tracing logic here (OpenTelemetry, X-Ray, etc.)
        return func(*args, **kwargs)

    return wrapper
