# PowerShell setup script for LocalStack environment
# This script creates the necessary AWS resources for local testing

$ErrorActionPreference = "Stop"

# Configuration
$LOCALSTACK_ENDPOINT = "http://localhost:4566"
$AWS_REGION = "us-east-1"
$QUEUE_NAME = "agent-events-queue"
$DLQ_NAME = "agent-events-dlq"
$SECRET_NAME = "agent-event-processor/database"

Write-Host "Setting up LocalStack environment for Agent Event Processor..."

# Configure AWS CLI for LocalStack
$env:AWS_ACCESS_KEY_ID = "test"
$env:AWS_SECRET_ACCESS_KEY = "test"
$env:AWS_DEFAULT_REGION = $AWS_REGION

# Wait for LocalStack to be ready
Write-Host "Waiting for LocalStack to be ready..."
do {
    try {
        $response = Invoke-WebRequest -Uri "$LOCALSTACK_ENDPOINT/_localstack/health" -UseBasicParsing
        $health = $response.Content | ConvertFrom-Json
        if ($health.services.sqs -eq "running") {
            break
        }
    }
    catch {
        Write-Host "Waiting for LocalStack..."
        Start-Sleep -Seconds 2
    }
} while ($true)

Write-Host "LocalStack is ready!"

# Create SQS Dead Letter Queue
Write-Host "Creating SQS Dead Letter Queue..."
aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs create-queue --queue-name $DLQ_NAME --region $AWS_REGION

$DLQ_URL = aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs get-queue-url --queue-name $DLQ_NAME --region $AWS_REGION --query 'QueueUrl' --output text

$DLQ_ARN = aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs get-queue-attributes --queue-url $DLQ_URL --attribute-names QueueArn --region $AWS_REGION --query 'Attributes.QueueArn' --output text

Write-Host "Created DLQ: $DLQ_ARN"

# Create main SQS Queue with DLQ configuration
Write-Host "Creating main SQS Queue..."
$redrive_policy = @{
    deadLetterTargetArn = $DLQ_ARN
    maxReceiveCount = 3
} | ConvertTo-Json -Compress

# Escape the JSON for command line
$escaped_policy = $redrive_policy -replace '"', '\"'
aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs create-queue --queue-name $QUEUE_NAME --attributes "RedrivePolicy=$escaped_policy" --region $AWS_REGION

$QUEUE_URL = aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs get-queue-url --queue-name $QUEUE_NAME --region $AWS_REGION --query 'QueueUrl' --output text

Write-Host "Created Queue: $QUEUE_URL"

# Create SSM Parameter Store parameter for database
Write-Host "Creating database parameter..."
$secret_value = @{
    host = "postgres"
    port = 5432
    database = "acd"
    username = "test_user"
    password = "test_password"
} | ConvertTo-Json -Compress

try {
    aws --endpoint-url=$LOCALSTACK_ENDPOINT ssm put-parameter --name $SECRET_NAME --value $secret_value --type SecureString --region $AWS_REGION
}
catch {
    # Parameter might already exist, try to update it
    aws --endpoint-url=$LOCALSTACK_ENDPOINT ssm put-parameter --name $SECRET_NAME --value $secret_value --type SecureString --overwrite --region $AWS_REGION
}

Write-Host "Created secret: $SECRET_NAME"

# Create IAM role for Lambda
Write-Host "Creating IAM role..."
$assume_role_policy = @{
    Version = "2012-10-17"
    Statement = @(
        @{
            Effect = "Allow"
            Principal = @{
                Service = "lambda.amazonaws.com"
            }
            Action = "sts:AssumeRole"
        }
    )
} | ConvertTo-Json -Depth 10 -Compress

try {
    aws --endpoint-url=$LOCALSTACK_ENDPOINT iam create-role --role-name lambda-role --assume-role-policy-document $assume_role_policy --region $AWS_REGION
}
catch {
    Write-Host "IAM role may already exist"
}

# Create CloudWatch Log Group
Write-Host "Creating CloudWatch Log Group..."
aws --endpoint-url=$LOCALSTACK_ENDPOINT logs create-log-group --log-group-name "/aws/lambda/agent-event-processor" --region $AWS_REGION

# Build and deploy Lambda function
Write-Host "Building Lambda function..."
$SCRIPT_DIR = Split-Path -Parent $MyInvocation.MyCommand.Definition
$PROJECT_DIR = Split-Path -Parent $SCRIPT_DIR

# Build the Lambda package using bash script (requires WSL or Git Bash on Windows)
$BUILD_SCRIPT = Join-Path $SCRIPT_DIR "build_lambda.sh"
if (Get-Command bash -ErrorAction SilentlyContinue) {
    # Convert Windows path to Unix-style path for bash
    $UNIX_BUILD_SCRIPT = $BUILD_SCRIPT -replace '\\', '/' -replace 'C:', '/c'
    bash $UNIX_BUILD_SCRIPT
} else {
    Write-Host "Warning: bash not found. Please run build_lambda.sh manually or install Git Bash/WSL"
    Write-Host "Skipping Lambda deployment..."
    Write-Host ""
    Write-Host "LocalStack setup completed (without Lambda)!"
    Write-Host "SQS Queue URL: $QUEUE_URL"
    Write-Host "Database Secret: $SECRET_NAME"
    exit 0
}

# Deploy Lambda function
Write-Host "Deploying Lambda function to LocalStack..."
$LAMBDA_ZIP = Join-Path (Split-Path -Parent $PROJECT_DIR) "smart-analytics-acd-processor.zip"

if (-not (Test-Path $LAMBDA_ZIP)) {
    Write-Host "Error: Lambda package not found at $LAMBDA_ZIP"
    exit 1
}

aws --endpoint-url=$LOCALSTACK_ENDPOINT lambda create-function --function-name agent-event-processor --runtime python3.12 --role arn:aws:iam::123456789012:role/lambda-execution-role --handler lambda_function.lambda_handler --zip-file fileb://$LAMBDA_ZIP --timeout 300 --memory-size 512 --environment Variables="{REDSHIFT_SECRET_NAME=$SECRET_NAME,AWS_REGION=$AWS_REGION}" --region $AWS_REGION

Write-Host "Lambda function deployed successfully!"

# Get SQS queue ARN for event source mapping
$QUEUE_ARN = aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs get-queue-attributes --queue-url $QUEUE_URL --attribute-names QueueArn --region $AWS_REGION --query 'Attributes.QueueArn' --output text

# Create event source mapping (SQS trigger)
Write-Host "Creating SQS trigger for Lambda function..."
aws --endpoint-url=$LOCALSTACK_ENDPOINT lambda create-event-source-mapping --event-source-arn $QUEUE_ARN --function-name agent-event-processor --batch-size 10 --maximum-batching-window-in-seconds 5 --region $AWS_REGION

Write-Host "SQS trigger created successfully!"

Write-Host ""
Write-Host "LocalStack setup completed!"
Write-Host ""
Write-Host "Resources created:"
Write-Host "  - SQS Queue: $QUEUE_URL"
Write-Host "  - SQS DLQ: $DLQ_URL"
Write-Host "  - Secret: $SECRET_NAME"
Write-Host "  - Log Group: /aws/lambda/agent-event-processor"
Write-Host "  - Lambda Function: agent-event-processor"
Write-Host "  - SQS Trigger: $QUEUE_ARN -> agent-event-processor"
Write-Host ""
Write-Host "Environment variables for testing:"
Write-Host "  `$env:AWS_ENDPOINT_URL = '$LOCALSTACK_ENDPOINT'"
Write-Host "  `$env:REDSHIFT_SECRET_NAME = '$SECRET_NAME'"
Write-Host "  `$env:AWS_REGION = '$AWS_REGION'"
Write-Host ""
Write-Host "To send test messages:"
Write-Host "  .\test_lambda.ps1"
