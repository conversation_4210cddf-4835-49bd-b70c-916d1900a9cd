"""Configuration settings for the Agent Event Processor Lambda function.

This module provides centralized configuration management using environment variables.
"""

from functools import lru_cache

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class DatabaseSettings(BaseSettings):
    """Database connection settings for Redshift Data API."""

    # Redshift connection settings
    cluster_identifier: str = Field(
        default="dev-us-smartanalytics-common-redshift",
        description="Redshift cluster identifier",
    )
    redshift_database: str = Field(default="dev", description="Redshift database name")
    redshift_user: str = Field(default="solacom", description="Redshift database user")
    query_group: str = Field(
        default="agent-event-processor",
        description="Redshift query group for resource management",
    )
    query_timeout: int = Field(
        default=300, description="Database query timeout in seconds"
    )

    model_config = SettingsConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        extra="forbid",
        env_prefix="DATABASE_",
    )


class AWSSettings(BaseSettings):
    """AWS service configuration settings."""

    region: str = Field(default="us-east-1", description="AWS region")

    model_config = SettingsConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        env_prefix="AWS_",
        extra="forbid",
    )


class LoggingSettings(BaseSettings):
    """Logging configuration settings."""

    level: str = Field(default="INFO", description="Log level")

    model_config = SettingsConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        env_prefix="LOG_",
        extra="forbid",
    )


class ClientSettings(BaseSettings):
    """Client-specific configuration settings."""

    name: str = Field(
        default="DefaultClient", description="Client name for tenant identification"
    )
    timezone: str = Field(
        default="UTC",
        description="Client timezone (e.g., America/New_York, America/Chicago)",
    )

    model_config = SettingsConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        env_prefix="CLIENT_",
        extra="forbid",
    )


class Settings(BaseSettings):
    """Main application settings."""

    # Environment
    environment: str = Field(default="dev", description="Deployment environment")

    # Nested settings
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    aws: AWSSettings = Field(default_factory=AWSSettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)
    client: ClientSettings = Field(default_factory=ClientSettings)

    model_config = SettingsConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        extra="forbid",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
    )


@lru_cache
def get_settings() -> Settings:
    """Get application settings with caching.

    Returns:
        Settings: Cached application settings instance.

    Note:
        Settings are cached to avoid repeated environment variable parsing
        during Lambda container reuse.
    """
    return Settings()
