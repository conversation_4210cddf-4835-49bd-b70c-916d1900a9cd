[mypy]
# Global options
python_version = 3.12
warn_return_any = false
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = false
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

# Allow gradual typing
disallow_any_unimported = false
disallow_any_expr = false
disallow_any_decorated = false
disallow_any_explicit = false
disallow_any_generics = false
disallow_subclassing_any = false

# Import discovery
namespace_packages = true
explicit_package_bases = true

# Error output
show_column_numbers = true
show_error_context = true
color_output = true
error_summary = true

# Ignore missing imports for third-party libraries
ignore_missing_imports = false

# Per-module options
[mypy-tests.*]
disallow_untyped_defs = false
disallow_incomplete_defs = false
ignore_errors = false

[mypy-xmltodict]
ignore_missing_imports = false

[mypy-boto3.*]
ignore_missing_imports = false

[mypy-botocore.*]
ignore_missing_imports = false

[mypy-psycopg2.*]
ignore_missing_imports = false

[mypy-aws_lambda_powertools.*]
ignore_missing_imports = false

[mypy-aws_lambda_typing.*]
ignore_missing_imports = false

[mypy-aws_xray_sdk.*]
ignore_missing_imports = false

[mypy-tenacity.*]
ignore_missing_imports = false

[mypy-pytz.*]
ignore_missing_imports = false

[mypy-pydantic.*]
ignore_missing_imports = false

[mypy-pydantic_settings.*]
ignore_missing_imports = false
