[flake8]
max-line-length = 88
extend-ignore =
    # E203: whitespace before ':' (conflicts with black)
    E203,
    # W503: line break before binary operator (conflicts with black)
    W503,
    # E501: line too long (handled by black)
    E501,
    # Move application import 'example' into a type-checking block
    TC001
    # TC002: Move third-party import 'example' into a type-checking block
    TC002
    # Move built-in import 'datetime.datetime' into a type-checking block
    TC003
exclude =
    .git,
    __pycache__,
    .venv,
    venv,
    build,
    dist,
    .eggs,
    *.egg-info,
    .mypy_cache,
    .pytest_cache,
    .coverage
per-file-ignores =
    # Ignore import order in __init__.py files
    __init__.py:F401
    # Ignore unused imports in test files
    test_*.py:F401,F811
max-complexity = 10
docstring-convention = google
import-order-style = google
