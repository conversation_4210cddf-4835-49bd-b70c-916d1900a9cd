# PowerShell script to build Lambda deployment packages
# Usage: .\build_lambda.ps1 -LambdaName "agent-event-processor" -OutputDir "dist"

param(
    [Parameter(Mandatory=$true)]
    [string]$LambdaName,
    
    [Parameter(Mandatory=$true)]
    [string]$OutputDir,
    
    [string]$PythonVersion = "3.12"
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Colors for output
$Green = "`e[32m"
$Red = "`e[31m"
$Yellow = "`e[33m"
$Blue = "`e[34m"
$Reset = "`e[0m"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = $Reset)
    Write-Host "${Color}${Message}${Reset}"
}

function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    } catch {
        return $false
    }
}

# Validate inputs
Write-ColorOutput "Building Lambda package: $LambdaName" $Blue

$LambdaDir = "lambdas/$LambdaName"
if (-not (Test-Path $LambdaDir)) {
    Write-ColorOutput "ERROR: Lambda directory not found: $LambdaDir" $Red
    exit 1
}

# Check for required tools
$RequiredTools = @("python", "pip")
foreach ($tool in $RequiredTools) {
    if (-not (Test-Command $tool)) {
        Write-ColorOutput "ERROR: Required tool not found: $tool" $Red
        exit 1
    }
}

# Verify Python version
$PythonVersionOutput = python --version 2>&1
Write-ColorOutput "Using Python: $PythonVersionOutput" $Yellow

# Create output directory
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
    Write-ColorOutput "Created output directory: $OutputDir" $Green
}

# Change to lambda directory
Push-Location $LambdaDir

try {
    Write-ColorOutput "Building package for $LambdaName..." $Blue

    # Create temporary build directory
    $BuildDir = "build"
    if (Test-Path $BuildDir) {
        Remove-Item -Recurse -Force $BuildDir
    }
    New-Item -ItemType Directory -Path $BuildDir | Out-Null

    # Copy source code
    Write-ColorOutput "Copying source code..." $Yellow
    Copy-Item -Recurse "src/*" $BuildDir

    # Install dependencies
    if (Test-Path "requirements.txt") {
        Write-ColorOutput "Installing dependencies..." $Yellow
        pip install --target $BuildDir --requirement requirements.txt --no-deps --quiet --upgrade
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to install dependencies"
        }

        # Clean up __pycache__ directories to avoid duplicates
        Get-ChildItem -Path $BuildDir -Name "__pycache__" -Recurse -Directory | ForEach-Object {
            Remove-Item -Path (Join-Path $BuildDir $_) -Recurse -Force
        }
    }

    # Create ZIP package
    $ZipPath = Join-Path (Resolve-Path "../../$OutputDir") "$LambdaName.zip"
    Write-ColorOutput "Creating ZIP package: $ZipPath" $Yellow

    # Remove existing zip if it exists
    if (Test-Path $ZipPath) {
        Remove-Item $ZipPath
    }

    # Create zip using PowerShell
    Push-Location $BuildDir
    try {
        # Get all files and directories, excluding duplicates
        $Files = Get-ChildItem -Recurse | Where-Object { $_.Name -ne "__pycache__" }
        if ($Files.Count -gt 0) {
            Compress-Archive -Path $Files.FullName -DestinationPath $ZipPath -CompressionLevel Optimal -Force
        } else {
            # If no files after filtering, compress everything
            Compress-Archive -Path "*" -DestinationPath $ZipPath -CompressionLevel Optimal -Force
        }
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to create ZIP package"
        }
    } finally {
        Pop-Location
    }
    
    # Verify package
    if (Test-Path $ZipPath) {
        $ZipSize = (Get-Item $ZipPath).Length
        $ZipSizeMB = [math]::Round($ZipSize / 1MB, 2)
        Write-ColorOutput "Package created successfully!" $Green
        Write-ColorOutput "Package size: $ZipSizeMB MB" $Green
        Write-ColorOutput "Package location: $ZipPath" $Green

        # List package contents (first 20 files)
        Write-ColorOutput "Package contents (first 20 files):" $Yellow
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        $zip = [System.IO.Compression.ZipFile]::OpenRead((Resolve-Path $ZipPath))
        $count = 0
        foreach ($entry in $zip.Entries) {
            if ($count -ge 20) {
                Write-ColorOutput "   ... and $($zip.Entries.Count - 20) more files" $Yellow
                break
            }
            Write-ColorOutput "   $($entry.FullName)" $Yellow
            $count++
        }
        $zip.Dispose()
    } else {
        throw "Package was not created"
    }

    # Cleanup
    if (Test-Path $BuildDir) {
        Remove-Item -Recurse -Force $BuildDir
    }

    Write-ColorOutput "Build completed successfully!" $Green
    
} catch {
    Write-ColorOutput "ERROR: Build failed: $($_.Exception.Message)" $Red
    exit 1
} finally {
    Pop-Location
}
