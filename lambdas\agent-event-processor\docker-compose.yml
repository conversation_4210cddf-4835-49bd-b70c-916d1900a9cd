# Docker setup for Agent Event Processor testing
version: '3.8'

services:
  # LocalStack for AWS services simulation
  localstack:
    container_name: agent-event-processor-localstack
    image: localstack/localstack:3.0
    ports:
      - "4566:4566"
      - "4510-4559:4510-4559"
    environment:
      # LocalStack configuration
      - DEBUG=1
      - SERVICES=lambda,sqs,logs,iam
      - DOCKER_HOST=unix:///var/run/docker.sock
      - HOST_TMP_FOLDER=${TMPDIR:-/tmp}/localstack
      - PERSISTENCE=1

      # AWS configuration
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test

      # Lambda configuration
      - LAMBDA_EXECUTOR=docker
      - LAMBDA_REMOVE_CONTAINERS=true

    volumes:
      - "${TMPDIR:-/tmp}/localstack:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4566/_localstack/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

    networks:
      - agent-event-network

  # Lambda function container for direct testing
  agent-event-processor:
    container_name: agent-event-processor-lambda
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "9000:8080"
    environment:
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_ENDPOINT_URL=http://localstack:4566
      - ENVIRONMENT=local
      - POWERTOOLS_SERVICE_NAME=agent-event-processor
      - POWERTOOLS_LOG_LEVEL=INFO
      # Redshift configuration (will fail gracefully in local testing)
      - DATABASE_CLUSTER_IDENTIFIER=localstack-redshift
      - DATABASE_REDSHIFT_DATABASE=dev
      - DATABASE_REDSHIFT_USER=test
      - DATABASE_QUERY_GROUP=agent-event-processor-local
      # Client configuration
      - CLIENT_NAME=TestClient
      - CLIENT_TIMEZONE=America/New_York
    networks:
      - agent-event-network
    depends_on:
      localstack:
        condition: service_healthy

networks:
  agent-event-network:
    driver: bridge
