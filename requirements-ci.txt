# Global CI/CD pipeline dependencies
# These are installed at the pipeline level for all jobs

# Code quality and formatting
ruff==0.8.4
flake8==7.0.0
mypy==1.8.0
bandit==1.7.5
pbr==7.0.1

# Testing framework
pytest==7.4.4
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-asyncio==0.23.2

# Additional linting plugins
flake8-docstrings==1.7.0
flake8-type-checking==2.9.1
flake8-bugbear==24.2.6
flake8-comprehensions==3.14.0
flake8-simplify==0.21.0

# Type stubs (common ones)
types-requests==2.32.0.20241016
types-boto3==1.0.2
types-psycopg2==2.9.21.20240201
types-pytz==2023.4.0.20240130
types-PyYAML==6.0.12.20250822
types-Pygments==2.19.0.20250809
types-aws-xray-sdk==2.14.0.20250708
types-colorama==0.4.15.20250801
types-docutils==0.22.0.20250822
types-jmespath==1.0.2.20250809
types-xmltodict==0.15.0.20250907

# Build and packaging tools
build==1.0.3
wheel==0.42.0

# Utilities
coverage==7.4.1
