# CloudWatch alarm outputs
output "lambda_errors_alarm_name" {
  description = "Name of the Lambda errors alarm"
  value       = aws_cloudwatch_metric_alarm.lambda_processors_errors.alarm_name
}

output "lambda_errors_alarm_arn" {
  description = "ARN of the Lambda errors alarm"
  value       = aws_cloudwatch_metric_alarm.lambda_processors_errors.arn
}

output "lambda_invocations_alarm_name" {
  description = "Name of the Lambda invocations decrease alarm"
  value       = aws_cloudwatch_metric_alarm.lambda_processors_invocations.alarm_name
}

output "lambda_invocations_alarm_arn" {
  description = "ARN of the Lambda invocations decrease alarm"
  value       = aws_cloudwatch_metric_alarm.lambda_processors_invocations.arn
}

output "lambda_duration_alarm_name" {
  description = "Name of the Lambda duration warning alarm"
  value       = aws_cloudwatch_metric_alarm.lambda_processors_duration_warn_alarm.alarm_name
}

output "lambda_duration_alarm_arn" {
  description = "ARN of the Lambda duration warning alarm"
  value       = aws_cloudwatch_metric_alarm.lambda_processors_duration_warn_alarm.arn
}
