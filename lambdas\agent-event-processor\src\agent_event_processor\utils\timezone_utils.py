"""Timezone conversion utilities for agent event processing.

This module provides timezone conversion and dimension key generation
for proper local time handling in the data warehouse.
"""

from datetime import datetime

import pytz
from aws_lambda_powertools import Logger

from src.agent_event_processor.config.settings import get_settings

logger = Logger()


def get_client_timezone() -> str:
    """Get client timezone from environment configuration.

    Returns:
        str: Timezone name (e.g., 'America/New_York', 'America/Chicago').
    """
    settings = get_settings()
    client_timezone = settings.client.timezone

    logger.debug("Retrieved client timezone", client_timezone=client_timezone)

    return client_timezone


def convert_to_tenant_timezone(
    utc_timestamp: datetime, tenant_timezone: str
) -> datetime:
    """Convert UTC timestamp to tenant's local timezone.

    Args:
        utc_timestamp: UTC timestamp to convert.
        tenant_timezone: Target timezone name.

    Returns:
        datetime: Timestamp converted to tenant timezone.

    Raises:
        ValueError: If timezone is invalid.
    """
    try:
        # Ensure UTC timestamp is timezone-aware
        if (
            utc_timestamp.tzinfo is None
            or utc_timestamp.tzinfo.utcoffset(utc_timestamp) is None
        ):
            utc_timestamp = utc_timestamp.replace(tzinfo=pytz.UTC)

        # Convert to tenant timezone
        tenant_tz = pytz.timezone(tenant_timezone)
        local_timestamp = utc_timestamp.astimezone(tenant_tz)

        logger.debug(
            "Converted timestamp to tenant timezone",
            utc_time=utc_timestamp.isoformat(),
            local_time=local_timestamp.isoformat(),
            timezone=tenant_timezone,
        )

        return local_timestamp

    except Exception as e:
        logger.error(
            "Failed to convert timezone",
            utc_timestamp=utc_timestamp.isoformat(),
            tenant_timezone=tenant_timezone,
            error=str(e),
        )
        raise ValueError(f"Invalid timezone conversion: {e}") from e


def generate_dimension_keys(local_timestamp: datetime) -> tuple[int, int]:
    """Generate date and time dimension keys from local timestamp.

    Args:
        local_timestamp: Timestamp in tenant's local timezone.

    Returns:
        Tuple[int, int]: (date_key, time_key) in YYYYMMDD and HHMMSS format.
    """
    # Generate date key (YYYYMMDD)
    date_key = int(local_timestamp.strftime("%Y%m%d"))

    # Generate time key (HHMMSS)
    time_key = int(local_timestamp.strftime("%H%M%S"))

    logger.debug(
        "Generated dimension keys",
        timestamp=local_timestamp.isoformat(),
        date_key=date_key,
        time_key=time_key,
    )

    return date_key, time_key


def get_shift_date_key(local_timestamp: datetime, shift_start_hour: int = 6) -> int:
    """Generate shift date key based on shift boundaries.

    For 24-hour operations, shifts often start at 6 AM. Events before 6 AM
    are considered part of the previous day's shift.

    Args:
        local_timestamp: Timestamp in tenant's local timezone.
        shift_start_hour: Hour when new shift starts (default: 6 AM).

    Returns:
        int: Shift date key in YYYYMMDD format.
    """
    shift_date = local_timestamp

    # If before shift start time, use previous day
    if local_timestamp.hour < shift_start_hour:
        shift_date = local_timestamp.replace(day=local_timestamp.day - 1)

    shift_date_key = int(shift_date.strftime("%Y%m%d"))

    logger.debug(
        "Generated shift date key",
        timestamp=local_timestamp.isoformat(),
        shift_start_hour=shift_start_hour,
        shift_date_key=shift_date_key,
    )

    return shift_date_key
