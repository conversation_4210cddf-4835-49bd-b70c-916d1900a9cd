{"Records": [{"messageId": "test-message-id-001", "receiptHandle": "test-receipt-handle-001", "body": "<LogEvent xmlns=\"http://solacom.com/Logging\">\n    <timestamp>2025-02-05T11:44:18.031Z</timestamp>\n    <agencyOrElement>Brandon911</agencyOrElement>\n    <agent>dycj</agent>\n    <eventType>Login</eventType>\n    <login>\n        <mediaLabel>_ML_194D5ECDE50C0001C46A@BrandonMB</mediaLabel>\n        <uri>tel:+2045553006</uri>\n        <agentRole>Rural - CT</agentRole>\n        <tenantGroup>Brandon911</tenantGroup>\n        <operatorId>6</operatorId>\n        <workstation>OP6</workstation>\n        <deviceName>Headset</deviceName>\n        <reason>normal</reason>\n    </login>\n</LogEvent>", "attributes": {"ApproximateReceiveCount": "1", "SentTimestamp": "1643723058000", "SenderId": "123456789012", "ApproximateFirstReceiveTimestamp": "1643723058000"}, "messageAttributes": {}, "md5OfBody": "test-md5-hash", "eventSource": "aws:sqs", "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:test-queue", "awsRegion": "us-east-1"}]}