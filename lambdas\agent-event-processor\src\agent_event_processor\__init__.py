"""Agent Event Processor Lambda Function.

This package provides a AWS Lambda function for processing
agent events from SQS queues and populating Amazon Redshift dimension tables.

The processor handles the following event types:
- Login: Agent workstation login
- Logout: Agent workstation logout
- AgentAvailable: Agent becomes available
- AgentBusiedOut: Agent becomes busy
- ACDLogin: Agent joins ACD queue
- ACDLogout: Agent leaves ACD queue

Architecture:
- Event-driven processing with SQS triggers
- Star schema data warehouse design
- SCD Type 2 dimension management
- Comprehensive error handling and retry logic
- Structured logging and monitoring
"""

__version__ = "1.0.0"
__author__ = "Smart Analytics Team"
