"""Main Lambda handler for processing agent events from SQS.

This module provides the entry point for the AWS Lambda function that processes
agent events from SQS queues and populates Redshift dimension tables.
"""

import json
import time
from typing import Any

from aws_lambda_powertools import Logger, Tracer
from aws_lambda_typing import context
from aws_lambda_typing.events import SQSEvent
from aws_lambda_typing.events.sqs import SQSMessage

from src.agent_event_processor.config.settings import Settings, get_settings
from src.agent_event_processor.models.events import AgentEvent
from src.agent_event_processor.services.event_processor import EventProcessor
from src.agent_event_processor.utils.xml_parser import AgentEventXMLParser

# Global variables for Lambda container reuse - initialized at module load
_settings: Settings = get_settings()
_event_processor: EventProcessor = EventProcessor(_settings)

# Initialize Lambda Powertools
logger = Logger(service="agent-event-processor")
tracer = Tracer(service="agent-event-processor")


def _prepare_event_data(json_data: dict[str, Any]) -> dict[str, Any]:
    """Prepare hierarchical JSON data for the AgentEvent model with validation.

    Args:
        json_data: Hierarchical JSON data from XML conversion

    Returns:
        Data structured for AgentEvent with preserved hierarchy in event_data

    Raises:
        ValueError: If data structure is invalid
    """
    if not isinstance(json_data, dict):
        raise ValueError("JSON data must be a dictionary")

    # Define core fields that belong at the top level
    core_fields = {"timestamp", "eventType", "agencyOrElement", "agent"}

    # Extract core fields
    event_fields = {}
    event_data = {}

    # Separate core fields from event-specific data
    for key, value in json_data.items():
        # Skip XML namespace attributes
        if key.startswith("@"):
            continue

        if key in core_fields:
            event_fields[key] = value
        else:
            event_data[key] = value

    # Combine core fields with event_data
    result = event_fields.copy()
    result["event_data"] = event_data
    logger.info("Event data prepared for processing", result=result)
    return result


def _process_single_record(
    record: SQSMessage, processor: EventProcessor, lambda_context: context.Context
) -> dict[str, Any]:
    """Process a single SQS record.

    Args:
        record: SQS record to process
        processor: Event processor instance
        lambda_context: Lambda context

    Returns:
        Dict with processing result

    Raises:
        Exception: If processing fails
    """
    # Validate record size
    record_body = record.get("body", "")
    if not record_body:
        raise ValueError("Record body is empty")
    # Extract and convert XML to JSON
    xml_content = AgentEventXMLParser.extract_xml_from_sqs_message(record_body)
    json_data = AgentEventXMLParser.xml_to_json(xml_content)

    # Prepare hierarchical data for AgentEvent model
    event_data = _prepare_event_data(json_data)

    # Validate with Pydantic model
    agent_event = AgentEvent.model_validate(event_data)

    # Process the event (store to database)
    success = processor.process_single_event(agent_event, lambda_context)

    if not success:
        raise Exception(f"Failed to process event for agent {agent_event.agent}")

    return {
        "success": True,
        "event_type": agent_event.event_type,
        "agent": agent_event.agent,
        "message_id": record.get("messageId"),
    }


@logger.inject_lambda_context
@tracer.capture_lambda_handler
def lambda_handler(event: SQSEvent, lambda_context: context.Context) -> dict[str, Any]:
    """Main Lambda handler for processing agent events from SQS.

    This function processes batches of agent events from SQS, validates them,
    and populates the appropriate Redshift dimension and fact tables.

    Args:
        event: SQS event containing Records array with agent events.
        lambda_context: Lambda execution context with request metadata.

    Returns:
        Dict containing processing results and any failed message identifiers
        for partial batch failure handling.

    Raises:
        Exception: Re-raises any unhandled exceptions after logging.
    """
    start_time = time.time()

    try:
        records = event.get("Records", [])

        logger.info(
            "Processing SQS batch",
            record_count=len(records),
            memory_limit_mb=lambda_context.memory_limit_in_mb,
            remaining_time_ms=lambda_context.get_remaining_time_in_millis(),
        )

        # Initialize counters and tracking
        successful_count, failed_count = 0, 0
        failed_message_ids = []
        # Get event processor

        for _, record in enumerate(records):
            try:
                result = _process_single_record(
                    record, _event_processor, lambda_context
                )
                successful_count += 1
                logger.info(
                    "Successfully processed event",
                    event_type=result["event_type"],
                    agent=result["agent"],
                    message_id=result["message_id"],
                )

            except Exception as e:
                failed_count += 1
                message_id = record.get("messageId", "unknown")
                failed_message_ids.append(message_id)

                logger.error(
                    "Failed to process record",
                    error=str(e),
                    message_id=message_id,
                    record_size=len(record.get("body", "")),
                    exc_info=True,
                )

        processing_time = (time.time() - start_time) * 1000

        if failed_count > 0:
            logger.warning(
                "Batch processing completed with failures",
                successful_count=successful_count,
                failed_count=failed_count,
                failed_message_ids=failed_message_ids,
                processing_time_ms=processing_time,
                batch_size=len(records),
                failure_rate=failed_count / len(records) if len(records) > 0 else 0,
            )
        else:
            logger.info(
                "Batch processing completed successfully",
                successful_count=successful_count,
                processing_time_ms=processing_time,
                batch_size=len(records),
            )

        batch_item_failures = [
            {"itemIdentifier": msg_id} for msg_id in failed_message_ids
        ]

        response = {
            "statusCode": 200,
            "body": json.dumps(
                {
                    "message": "Batch processing completed",
                    "successful_count": successful_count,
                    "failed_count": failed_count,
                    "processing_time_ms": processing_time,
                }
            ),
        }

        if batch_item_failures:
            response["batchItemFailures"] = batch_item_failures

        return response

    except Exception as e:
        # Log the error with full context
        processing_time = (time.time() - start_time) * 1000
        logger.error(
            "Lambda execution failed",
            error=str(e),
            error_type=type(e).__name__,
            processing_time_ms=processing_time,
            exc_info=True,
        )
        # Re-raise to trigger Lambda error handling
        raise
