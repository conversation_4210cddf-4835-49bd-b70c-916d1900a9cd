# Smart Analytics Processors

A data processing platform for smart analytics using AWS Lambda functions. This project provides scalable, secure, and monitored data processing capabilities for ACD (Automatic Call Distribution) data with multi-customer support.

## Architecture Overview

```mermaid
graph TB
    subgraph "AWS Cloud"
        PL[Push Lambda]
        subgraph "Message Processing"
            SQS[SQS Queues]
            DLQ[Dead Letter Queues]
        end

        subgraph "VPC - Private Subnets"
            Lambda[Lambda Processors<br/>Python 3.12]
            Redshift[(Amazon Redshift<br/>Data Warehouse)]
        end

        subgraph "Storage & Security"
            S3[S3 Bucket <br/>Lambda Code]
            KMS[AWS KMS<br/>Encryption Keys]
        end

        subgraph "Monitoring & Alerting"
            CW[CloudWatch<br/>Logs & Metrics]
            Alarms[CloudWatch Alarms]
            SNS[SNS Topics]
            Slack[Slack Notifications]
        end
    end

    PL --> SQS
    SQS --> Lambda
    DLQ --> Lambda
    S3 --> Lambda
    Lambda --> Redshift
    Lambda --> CW
    CW --> Alarms
    Alarms --> SNS
    SNS --> Slack
```

## Features

### Multi-Customer Architecture
- **Isolated Processing**: Per-customer Lambda functions and resources
- **Configurable Settings**: Customer-specific configurations and environment variables
- **Scalable Design**: Supports unlimited customers with consistent patterns

### Security & Compliance
- **VPC Deployment**: Lambda functions run in private subnets for secure database access
- **KMS Encryption**: All data encrypted at rest and in transit
- **Least Privilege IAM**: Minimal permissions following security best practices
- **Secrets Management**: Database credentials stored securely in AWS Secrets Manager

### Monitoring & Observability
- **CloudWatch Alarms**: Error detection, performance monitoring, and anomaly detection
- **Centralized Logging**: CloudWatch logs with 1-year retention
- **Real-time Alerts**: Slack integration for immediate incident response
- **Performance Metrics**: Duration, invocation, and error rate monitoring

### Scalability & Performance
- **Configurable Concurrency**: Reserved and maximum concurrency controls
- **Batch Processing**: Optimized SQS batch processing for efficiency
- **Auto-scaling**: Lambda auto-scaling based on queue depth
- **Dead Letter Queue**: Error handling and retry mechanisms

## Project Structure

```
smartanalytics-processors/
├── README.md                    # This file - project overview
├── CHANGELOG.md                 # Version history and changes
├── .gitlab-ci.yml               # CI/CD pipeline configuration
├── lambdas/                     # Lambda function source code
│   └── agent-event-processor/   # Agent event processing Lambda
├── scripts/                     # Optional local development scripts
├── shared/                      # Shared libraries and utilities
│   ├── go/                      # Go shared libraries
│   └── python/                  # Python shared libraries
└── terraform/                   # Infrastructure as Code
    ├── README.md                # Terraform documentation
    ├── main.tf                  # Root module configuration
    ├── variables.tf             # Input variables
    ├── locals.tf                # Local values
    ├── data.tf                  # Data sources
    ├── outputs.tf               # Output values
    ├── versions.tf              # Provider requirements
    ├── modules/                 # Reusable Terraform modules
    │   ├── acd-processor/       # ACD processor Lambda module
    │   │   ├── README.md        # Module documentation
    │   │   ├── main.tf          # Module configuration
    │   │   ├── variables.tf     # Module inputs
    │   │   ├── outputs.tf       # Module outputs
    │   │   ├── lambda.tf        # Lambda function resources
    │   │   ├── iam.tf           # IAM roles and policies
    │   │   └── data.tf          # Data sources
    │   └── alarms/              # CloudWatch alarms module
    │       ├── README.md        # Module documentation
    │       ├── main.tf          # Module configuration
    │       ├── variables.tf     # Module inputs
    │       ├── outputs.tf       # Module outputs
    │       ├── cloudwatch.tf    # CloudWatch alarm resources
    │       └── data.tf          # Data sources
```

## Data Flow

```mermaid
sequenceDiagram
    participant S3 as S3 Bucket
    participant SQS as SQS Queue
    participant Lambda as Lambda Processor
    participant Secrets as Secrets Manager
    participant Redshift as Redshift DW
    participant CW as CloudWatch
    participant SNS as SNS/Slack

    S3->>SQS: Send ACD data
    SQS->>Lambda: Trigger (batch/timeout)
    Lambda->>Secrets: Get DB credentials
    Secrets-->>Lambda: Return credentials
    Lambda->>Redshift: Process & store data
    Lambda->>CW: Log execution metrics

    alt Error occurs
        Lambda->>SQS: Message to DLQ
        CW->>SNS: Trigger alarm
        SNS->>Slack: Send alert
    end

    alt Success
        Lambda->>SQS: Delete processed messages
        Lambda->>CW: Log success metrics
    end
```

## Technology Stack

### Infrastructure
- **AWS Lambda**: Serverless compute for data processing
- **Amazon SQS**: Message queuing for reliable data ingestion
- **Amazon Redshift**: Data warehouse for analytics storage
- **AWS VPC**: Network isolation and security
- **Terraform**: Infrastructure as Code

### Security & Monitoring
- **AWS KMS**: Encryption key management
- **AWS Secrets Manager**: Secure credential storage
- **AWS CloudWatch**: Logging and monitoring
- **AWS SNS**: Notification service
- **AWS IAM**: Identity and access management

### Development
- **Python 3.12**: Lambda runtime
- **GitLab CI/CD**: Continuous integration and deployment
- **Slack**: Real-time alerting and notifications

## Prerequisites

1. **AWS Account** with appropriate permissions
2. **Terraform >= 1.6** installed
3. **AWS CLI** configured
4. **S3 bucket** for Lambda code (created separately)
5. **VPC and subnets** configured
6. **Redshift cluster** deployed

## Alarm Thresholds

| Metric | Warning | Critical | Action |
|--------|---------|----------|--------|
| Error Rate | > 1% | > 5% | Check logs, investigate code |
| Duration | > 10s | > 30s | Optimize code, increase memory |
| Queue Depth | > 100 | > 1000 | Scale up, check processing |
| DLQ Messages | > 0 | > 10 | Investigate failed messages |

## Configuration

### Customer Configuration Example

```hcl
service_customer_config = {
  customer1 = {
    s3_lambda_code_bucket_name = "lambda-code-bucket"
    kms_key_name              = "alias/lambda-code-key"

    acd = {
      enable                      = true
      queue_name                 = "customer1-acd-queue"
      dlq_name                   = "customer1-acd-dlq"
      subnet_ids                 = ["subnet-12345678", "subnet-87654321"]
      vpc_id                     = "vpc-12345678"
      redshift_secret_name       = "redshift/customer1"
      redshift_cluster_identifier = "customer1-redshift"
      environment_variables = {
        REDSHIFT_DATABASE = "customer1_analytics"
        LOG_LEVEL        = "INFO"
      }
    }

    tags = {
      Customer    = "customer1"
      Environment = "prod"
    }
  }
}
```

## Security Best Practices

### Network Security
- Lambda functions deployed in private subnets
- Security groups with minimal required access
- VPC endpoints for AWS services (recommended)
- No direct internet access from Lambda

### Data Protection
- All data encrypted at rest using KMS
- TLS 1.2+ for data in transit
- Secrets Manager for database credentials
- Regular key rotation policies

### Access Control
- Least privilege IAM policies
- Resource-based policies where applicable
- Cross-account access controls
- Regular access reviews

---

**Last Updated**: 2025-08-11
**Version**: 1.0.0
**Maintainer**: Smart Analytics Team
