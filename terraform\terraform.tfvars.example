# Example Terraform variables file

# AWS Region
region = "us-east-1"

# Environment (dev, qa, prod)
environment = "dev"

# Country (us, ca)
country = "us"

# Global tags applied to all resources
global_tags = {
  Environment = "dev"
  Project     = "smart-analytics"
  Owner       = "csa"
  ManagedBy   = "terraform"
}

# SNS topic for Slack error notifications
slack_errors_alarm_topic_name = "csa-critical-alarms"

# Project directory (usually set by CI/CD)
project_dir = "."

# Customer configuration
service_customer_config = {
  customer1 = {
    # S3 bucket for Lambda code (created in separate repository)
    s3_lambda_code_bucket_name = "lambda-code-bucket"

    # KMS key for encryption
    kms_key_name = "alias/lambda-code-key"

    # ACD processor configuration
    acd = {
      # Enable ACD processing for this customer
      enable = true

      # SQS queue names
      queue_name = "customer1-acd-queue"
      dlq_name   = "customer1-acd-dlq"

      # VPC configuration
      subnet_ids         = ["subnet-12345678", "subnet-87654321"]
      vpc_id             = "vpc-12345678"

      # Redshift configuration
      redshift_secret_name         = "redshift/customer1"
      redshift_cluster_identifier  = "redshift-cluster"

      # Lambda environment variables
      environment_variables = {
        REDSHIFT_DATABASE = "customer1"
        LOG_LEVEL        = "INFO"
        BATCH_SIZE       = "10"
      }
    }

    # Customer-specific tags
    tags = {
      Customer   = "customer1"
      CostCenter = "analytics"
    }
  }
}
