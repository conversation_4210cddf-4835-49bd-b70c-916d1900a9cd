# Terraform files
*.tfstate
*.tfstate.*
*.tfplan
*.tfplan.*
.terraform/
.terraform.lock.hcl
terraform.tfvars
terraform.tfvars.json
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Terragrunt files
.terragrunt-cache/
terragrunt.hcl.backup

# Lambda deployment packages
*.zip
*.jar
*.tar.gz

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Python files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Logs
*.log
logs/
temp/
tmp/

# Backup files
*.backup
*.bak
*.history

# Local configuration files
.env.local
.env.development.local
.env.test.local
.env.production.local

# AWS credentials (should never be committed)
.aws/
aws-credentials
credentials

# Sensitive files
secrets/
*.pem
*.key
*.crt
*.p12
*.pfx

# Coverage reports
coverage.xml
coverage/
htmlcov/
.coverage
