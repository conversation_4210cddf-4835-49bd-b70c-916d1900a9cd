# Deployment CI/CD Pipeline for Smart Analytics Processors
# This pipeline consumes Lambda packages from the build pipeline and deploys infrastructure

variables:
  # Smart Analytics Processors build pipeline configuration
  SMARTANALYTICS_PROCESSORS_PROJECT_ID: "90"  # Replace with actual project ID
  SMARTANALYTICS_PROCESSORS_VERSION: "latest"  # Or specific version like "v1.0.0"
  
  # Terragrunt configuration
  DOCKER_TERRAGRUNT_TAG: aws-tf-1.10.3-tg-0.71.1
  PLAN: plan.tfplan
  JSON_PLAN_FILE: tfplan.json
  VAULT_VERSION: 1.13.2
  CHECKSUMS: |-
    f7930279de8381de7c532164b4a4408895d9606c0d24e2e9d2f9acb5dfe99b3c  /tmp/vault.zip
  TF_PLUGIN_CACHE_DIR: ${CI_PROJECT_DIR}/.terraform.d/plugin-cache
  TFLINT_PLUGIN_DIR: ${CI_PROJECT_DIR}/.tflint.d/plugins

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
      when: never
    - if: $CI_COMMIT_BRANCH
    - if: $CI_COMMIT_TAG =~ /^rel-/

default:
  cache:
    - key: terraform-cache
      paths:
        - ${CI_PROJECT_DIR}/.tflint.d/plugins
        - ${CI_PROJECT_DIR}/.terraform.d/plugin-cache

.before_script_template:
  before_script:
    - mkdir -p ${CI_PROJECT_DIR}/.terraform.d/plugin-cache || true
    - mkdir -p ${CI_PROJECT_DIR}/.tflint.d/plugins || true
    # Alias for terraform plan integration with Gitlab MR.
    - shopt -s expand_aliases
    - alias convert_report="jq -r '([.resource_changes[]?.change.actions?]|flatten)|{\"create\":(map(select(.==\"create\"))|length),\"update\":(map(select(.==\"update\"))|length),\"delete\":(map(select(.==\"delete\"))|length)}'"
    
    # Install Vault
    - curl -L -o /tmp/vault.zip "https://releases.hashicorp.com/vault/${VAULT_VERSION}/vault_${VAULT_VERSION}_linux_amd64.zip"
    - sha256sum /tmp/vault.zip
    - echo ${CHECKSUMS}
    - echo ${CHECKSUMS} | (cd /tmp; sha256sum -c -w -) || exit 1
    - unzip /tmp/vault.zip -d /tmp
    - install -b -c -v /tmp/vault /usr/bin/
    - vault version
    
    # Dump versions & info
    - terragrunt -version
    - terraform -version
    - tflint --version
    - go version
    - echo ${CI_PROJECT_NAMESPACE}
    - echo ${CI_PROJECT_NAME}
    
    # Set Git to use CI Token for cloning.
    - git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.nsoc.state911.net".insteadOf "ssh://****************************"
    
    # Download Lambda packages from smartanalytics-processors
    - mkdir -p /tmp/lambda_packages/artifacts
    - |
      PACKAGE_VERSION=${SMARTANALYTICS_PROCESSORS_VERSION}
      if [ -z "$PACKAGE_VERSION" ]; then
        PACKAGE_VERSION="latest"
      fi
      
      echo "Downloading Smart Analytics Processors Lambda packages version: $PACKAGE_VERSION"
      
      # Download package manifest first to get list of available functions
      echo "Downloading package manifest..."
      curl --location --header "JOB-TOKEN: $CI_JOB_TOKEN" \
        --output "/tmp/lambda_packages/package-manifest.json" \
        --create-dirs \
        "${CI_API_V4_URL}/projects/${SMARTANALYTICS_PROCESSORS_PROJECT_ID}/packages/generic/smartanalytics-processors/${PACKAGE_VERSION}/package-manifest.json" || true
      
      # Define Lambda functions to download
      # You can either hardcode these or read from the manifest
      LAMBDA_FUNCTIONS=("agent-event-processor")
      
      # If manifest exists, extract function names from it
      if [ -f "/tmp/lambda_packages/package-manifest.json" ]; then
        echo "Using package manifest to determine available functions"
        MANIFEST_FUNCTIONS=$(jq -r '.functions[].name' /tmp/lambda_packages/package-manifest.json 2>/dev/null || echo "")
        if [ -n "$MANIFEST_FUNCTIONS" ]; then
          LAMBDA_FUNCTIONS=($MANIFEST_FUNCTIONS)
        fi
        echo "Package manifest contents:"
        cat /tmp/lambda_packages/package-manifest.json | jq .
      fi
      
      echo "Downloading Lambda functions: ${LAMBDA_FUNCTIONS[*]}"
      
      # Download each Lambda package
      for func in "${LAMBDA_FUNCTIONS[@]}"; do
        echo "Downloading: ${func}.zip"
        curl --location --header "JOB-TOKEN: $CI_JOB_TOKEN" \
          --output "/tmp/lambda_packages/artifacts/${func}.zip" \
          --create-dirs \
          "${CI_API_V4_URL}/projects/${SMARTANALYTICS_PROCESSORS_PROJECT_ID}/packages/generic/smartanalytics-processors/${PACKAGE_VERSION}/${func}.zip"
        
        if [ $? -eq 0 ]; then
          echo "Successfully downloaded: ${func}.zip"
          # Verify the package
          if [ -f "/tmp/lambda_packages/artifacts/${func}.zip" ]; then
            ZIP_SIZE=$(stat -c%s "/tmp/lambda_packages/artifacts/${func}.zip" 2>/dev/null || stat -f%z "/tmp/lambda_packages/artifacts/${func}.zip" 2>/dev/null || echo "0")
            ZIP_SIZE_MB=$(echo "scale=2; $ZIP_SIZE / 1024 / 1024" | bc -l 2>/dev/null || echo "$(($ZIP_SIZE / 1024 / 1024))")
            echo "Package size: ${ZIP_SIZE_MB} MB"
            
            # List package contents (first 10 files)
            echo "Package contents (first 10 files):"
            unzip -l "/tmp/lambda_packages/artifacts/${func}.zip" | head -15
          fi
        else
          echo "Failed to download: ${func}.zip"
          exit 1
        fi
      done
      
      echo "All Lambda packages downloaded successfully!"
      echo "Downloaded packages:"
      ls -la /tmp/lambda_packages/artifacts/
  tags:
    - codebuild-gitlab-ci-${CI_PROJECT_NAMESPACE_SLUG}-$CI_PROJECT_ID-$CI_PIPELINE_IID-$CI_JOB_NAME
    - image:custom-linux-devopsinfra/docker-terragrunt:$DOCKER_TERRAGRUNT_TAG

stages:
  - validate
  - plan
  - deploy
  - remove

validate:
  stage: validate
  extends: .before_script_template
  script:
    - echo "Validating Terragrunt configuration..."
    - cd live/ && TERRAGRUNT_DISABLE_INIT=true terragrunt run-all validate --terragrunt-non-interactive
    - echo "Validation completed successfully"

.plan-template:
  stage: plan
  extends: .before_script_template
  resource_group: terraform_state
  rules:
    - when: never
  id_tokens:
    VAULT_TOKEN:
      aud: https://vault.int.state911.net
  script:
    - echo "Starting Terraform plan for ${ENVIRONMENT}..."
    - export VAULT_ADDR=https://vault.int.state911.net
    - export VAULT_TOKEN="$(vault write -field=token auth/jwt/login role=ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME} jwt=${VAULT_TOKEN})"
    - export AWS_ACCESS_KEY_ID=$(vault kv get -format=json secret/gitlab-ci/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}/${PARTITION}-ci-config | jq -r .data.data.access_key)
    - export AWS_SECRET_ACCESS_KEY=$(vault kv get -format=json secret/gitlab-ci/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}/${PARTITION}-ci-config | jq -r .data.data.secret_access_key)
    - export ROLE_ARN=$(vault kv get -format=json secret/gitlab-ci/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}/${PARTITION}-ci-config | jq -r ".data.data.ci_role_arn_list[\"$ACCOUNT\"]")
    - mkdir ${HOME}/.aws/
    - chmod 700 ${HOME}/.aws/
    # Setup default profile with region. Creds come from environment variables
    - (echo "[default]"; echo "region = ${REGION}";) >> ${HOME}/.aws/config
    - export AWS_REGION=${REGION}
    # Change to environment directory and run terragrunt
    - cd live/${PARTITION}/${ENVIRONMENT}
    - echo "Rendering Terragrunt configuration..."
    - terragrunt render-json --terragrunt-non-interactive --terragrunt-iam-role ${ROLE_ARN} --terragrunt-iam-assume-role-session-name "gitlab-ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME}"
    - echo "Terragrunt configuration:"
    - cat terragrunt_rendered.json | jq .
    - echo "Running Terraform plan..."
    - terragrunt plan --terragrunt-non-interactive --terragrunt-iam-role ${ROLE_ARN} --terragrunt-iam-assume-role-session-name "gitlab-ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME}" -out $(pwd)/${ENVIRONMENT}-${PLAN}
    - echo "Generating plan report..."
    - terragrunt show --terragrunt-non-interactive --terragrunt-iam-role ${ROLE_ARN} --terragrunt-iam-assume-role-session-name "gitlab-ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME}" --json $(pwd)/${ENVIRONMENT}-${PLAN} | convert_report > $(pwd)/${ENVIRONMENT}-${JSON_PLAN_FILE}
    - echo "Plan completed for ${ENVIRONMENT}"
  after_script:
    - rm -rf ${HOME}/.aws/
  artifacts:
    name: terraform-plan-${ENVIRONMENT}
    expire_in: 1 week
    paths:
      - live/${PARTITION}/${ENVIRONMENT}/${ENVIRONMENT}-${PLAN}
      - live/${PARTITION}/${ENVIRONMENT}/.terragrunt-cache/
    reports:
      terraform: live/${PARTITION}/${ENVIRONMENT}/${ENVIRONMENT}-${JSON_PLAN_FILE}

# Development environment plans
plan-dev-nsoc:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-nsoc
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "data-analytics" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-dev-nsoc-ca:
  extends: .plan-template
  variables:
    REGION: ca-central-1
    ENVIRONMENT: dev-nsoc-ca
    ACCOUNT: dev-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "data-analytics" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-us-gov-dev-ma:
  extends: .plan-template
  variables:
    REGION: us-gov-east-1
    ENVIRONMENT: us-gov-dev-ma
    ACCOUNT: us-gov-dev-ma
    PARTITION: aws-us-gov
  rules:
    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "data-analytics" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

# Production environment plans
plan-prod-nsoc:
  extends: .plan-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: prod-nsoc
    ACCOUNT: prod-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "data-analytics" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-prod-nsoc-ca:
  extends: .plan-template
  variables:
    REGION: ca-central-1
    ENVIRONMENT: prod-nsoc-ca
    ACCOUNT: prod-nsoc
    PARTITION: aws
  rules:
    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "data-analytics" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

plan-us-gov-prod-ma:
  extends: .plan-template
  variables:
    REGION: us-gov-east-1
    ENVIRONMENT: us-gov-prod-ma
    ACCOUNT: us-gov-prod-ma
    PARTITION: aws-us-gov
  rules:
    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
      when: never
    - if: $CI_PROJECT_NAMESPACE == "data-analytics" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

# Deploy template
.deploy-template:
  stage: deploy
  extends: .before_script_template
  resource_group: terraform_state
  rules:
    - when: never
  id_tokens:
    VAULT_TOKEN:
      aud: https://vault.int.state911.net
  script:
    - echo "Starting deployment for ${ENVIRONMENT}..."
    - export VAULT_ADDR=https://vault.int.state911.net
    - export VAULT_TOKEN="$(vault write -field=token auth/jwt/login role=ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME} jwt=${VAULT_TOKEN})"
    - export AWS_ACCESS_KEY_ID=$(vault kv get -format=json secret/gitlab-ci/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}/${PARTITION}-ci-config | jq -r .data.data.access_key)
    - export AWS_SECRET_ACCESS_KEY=$(vault kv get -format=json secret/gitlab-ci/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}/${PARTITION}-ci-config | jq -r .data.data.secret_access_key)
    - export ROLE_ARN=$(vault kv get -format=json secret/gitlab-ci/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}/${PARTITION}-ci-config | jq -r ".data.data.ci_role_arn_list[\"$ACCOUNT\"]")
    - mkdir ${HOME}/.aws/
    - chmod 700 ${HOME}/.aws/
    # Setup default profile with region. Creds come from environment variables
    - (echo "[default]"; echo "region = ${REGION}";) >> ${HOME}/.aws/config
    - export AWS_REGION=${REGION}
    # Change to environment directory and run terragrunt
    - cd live/${PARTITION}/${ENVIRONMENT}
    - echo "Rendering Terragrunt configuration..."
    - terragrunt render-json --terragrunt-non-interactive --terragrunt-iam-role ${ROLE_ARN} --terragrunt-iam-assume-role-session-name "gitlab-ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME}"
    - echo "Terragrunt configuration:"
    - cat terragrunt_rendered.json | jq .
    - echo "Applying Terraform plan..."
    - terragrunt apply --terragrunt-non-interactive --terragrunt-iam-role ${ROLE_ARN} --terragrunt-iam-assume-role-session-name "gitlab-ci-${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME}" $(pwd)/${ENVIRONMENT}-${PLAN}
    - echo "Deployment completed for ${ENVIRONMENT}"
  after_script:
    - rm -rf ${HOME}/.aws/

# Development environment deployments
deploy-dev-nsoc:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: dev-nsoc
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}
    deployment_tier: development
  rules:
    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-dev-nsoc

deploy-dev-nsoc-ca:
  extends: .deploy-template
  variables:
    REGION: ca-central-1
    ENVIRONMENT: dev-nsoc-ca
    ACCOUNT: dev-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}
    deployment_tier: development
  rules:
    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-dev-nsoc-ca

deploy-us-gov-dev-ma:
  extends: .deploy-template
  variables:
    REGION: us-gov-east-1
    ENVIRONMENT: us-gov-dev-ma
    ACCOUNT: us-gov-dev-ma
    PARTITION: aws-us-gov
  environment:
    name: ${ENVIRONMENT}
    deployment_tier: development
  rules:
    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-us-gov-dev-ma

# Production environment deployments
deploy-prod-nsoc:
  extends: .deploy-template
  variables:
    REGION: us-east-1
    ENVIRONMENT: prod-nsoc
    ACCOUNT: prod-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}
    deployment_tier: production
  rules:
    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-prod-nsoc

deploy-prod-nsoc-ca:
  extends: .deploy-template
  variables:
    REGION: ca-central-1
    ENVIRONMENT: prod-nsoc-ca
    ACCOUNT: prod-nsoc
    PARTITION: aws
  environment:
    name: ${ENVIRONMENT}
    deployment_tier: production
  rules:
    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-prod-nsoc-ca

deploy-us-gov-prod-ma:
  extends: .deploy-template
  variables:
    REGION: us-gov-east-1
    ENVIRONMENT: us-gov-prod-ma
    ACCOUNT: us-gov-prod-ma
    PARTITION: aws-us-gov
  environment:
    name: ${ENVIRONMENT}
    deployment_tier: production
  rules:
    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  needs:
    - plan-us-gov-prod-ma
