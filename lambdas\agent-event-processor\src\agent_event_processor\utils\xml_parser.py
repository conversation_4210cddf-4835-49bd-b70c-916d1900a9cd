"""XML to JSON converter for agent event processing using xmltodict.

This module provides utilities to convert raw XML agent events from SQS messages
to clean JSON objects while preserving hierarchy.
"""

import json
from typing import Any
from xml.parsers import expat as expat_parser

import xmltodict
from aws_lambda_powertools import Logger

logger = Logger()


class AgentEventXMLParser:
    """Converts XML agent events to clean JSON objects using xmltodict."""

    @classmethod
    def xml_to_json(cls, xml_content: str) -> dict[str, Any]:
        """Convert XML event to clean JSON object preserving hierarchy using xmltodict.

        Args:
            xml_content: Raw XML content from SQS message

        Returns:
            Clean JSON object with hierarchy preserved

        Raises:
            ValueError: If XML parsing fails or required fields are missing
        """
        try:
            # Convert XML to dictionary using xmltodict with enhanced security
            data = xmltodict.parse(
                xml_content,
                process_namespaces=False,  # Disable namespace processing for security
                namespace_separator=":",
                disable_entities=True,  # Disable entity processing for security
                expat=expat_parser,  # Use expat parser for better security
                process_comments=False,  # Disable comment processing
                strip_whitespace=True,  # Strip whitespace for cleaner data
            )
            # Extract the LogEvent content (remove root wrapper)
            if "LogEvent" in data:
                json_data = data["LogEvent"]
            else:
                json_data = data

            # Validate required fields exist
            required_fields = ["timestamp", "agencyOrElement", "agent", "eventType"]
            missing_fields = [f for f in required_fields if f not in json_data]
            if missing_fields:
                raise ValueError(f"Missing required fields: {missing_fields}")

            logger.info(
                "Successfully converted XML to JSON",
                event_type=json_data.get("eventType"),
                agent=json_data.get("agent"),
                agency=json_data.get("agencyOrElement"),
            )

            return json_data

        except Exception as e:
            logger.error(
                "XML to JSON conversion failed",
                error=str(e),
                xml_preview=xml_content[:500],
            )
            raise ValueError(f"Failed to parse XML: {e}") from e

    @classmethod
    def extract_xml_from_sqs_message(cls, sqs_body) -> str:
        """Extract XML content from SQS message body with security validations.

        The SQS message might be wrapped in JSON or contain the raw XML.

        Args:
            sqs_body: Raw SQS message body (string or dict)

        Returns:
            Extracted XML content
        """
        # Handle case where sqs_body is already a dict (from test payloads)
        if isinstance(sqs_body, dict):
            if "Message" in sqs_body:
                xml_content = sqs_body["Message"]
            else:
                raise ValueError("Dict body must contain 'Message' key")
        else:
            # Handle string body
            try:
                # Try to parse as JSON first (SNS -> SQS pattern)
                message_data = json.loads(sqs_body)

                # Check if it's an SNS message
                if "Message" in message_data:
                    xml_content = message_data["Message"]
                else:
                    # Direct SQS message with XML
                    xml_content = sqs_body

            except json.JSONDecodeError:
                # Raw XML content
                xml_content = sqs_body

        # Additional validation
        if not isinstance(xml_content, str):
            raise ValueError(f"XML content must be string, got {type(xml_content)}")

        xml_content = xml_content.strip()
        if not xml_content:
            raise ValueError("Empty XML content after extraction")

        return xml_content
