repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-json
      - id: check-merge-conflict
      - id: debug-statements
      - id: check-docstring-first

  # Ruff: format first, then lint
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.12.12
    hooks:
      - id: ruff-format
      - id: ruff
        alias: ruff-check
        args: [--fix]  # auto-fix where safe

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.17.1
    hooks:
      - id: mypy
        additional_dependencies:
        - boto3-stubs[redshift-data]
        - types-requests
        - pydantic>=2
        - pydantic-settings>=2
        - aws-lambda-powertools>=2
        - aws-lambda-typing
        - xmltodict
        - types-xmltodict
        - pytz
        - types-pytz
        - tenacity
        - psycopg2-binary
        - types-psycopg2
        - pytest
        args:
          - --show-error-codes
          - --pretty

  - repo: https://github.com/PyCQA/bandit
    rev: 1.8.6  # or your preferred version
    hooks:
      - id: bandit
        args: ["-q", "-x", "tests"]
