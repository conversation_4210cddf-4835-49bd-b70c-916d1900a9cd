#!/usr/bin/env python3
"""Complete Database Initialization Script for Agent Event Processor.

This script creates all necessary tables, indexes, and initial data for the
agent event processing system with proper deduplication and SCD2 support.

Usage:
    python init_database_complete.py --cluster-id my-cluster --database mydb --user myuser

Environment Variables:
    DATABASE_CLUSTER_IDENTIFIER: Redshift cluster identifier
    DATABASE_REDSHIFT_DATABASE: Database name
    DATABASE_REDSHIFT_USER: Database user
    AWS_REGION: AWS region (default: us-east-1)
"""

import argparse
import logging
import os
import sys
from datetime import datetime

import boto3

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class DatabaseInitializer:
    """Database initialization with proper error handling and validation."""

    def __init__(
        self,
        cluster_identifier: str,
        database: str,
        user: str,
        region: str = "us-east-1",
    ):
        """Initialize database connection."""
        self.cluster_identifier = cluster_identifier
        self.database = database
        self.user = user
        self.region = region

        self.client = boto3.client("redshift-data", region_name=region)
        logger.info(
            f"Initialized for cluster: {cluster_identifier}, database: {database}"
        )

    def execute_sql(self, sql: str, description: str = "") -> bool:
        """Execute SQL with proper error handling."""
        try:
            logger.info(f"Executing: {description or sql[:50]}...")

            response = self.client.execute_statement(
                ClusterIdentifier=self.cluster_identifier,
                Database=self.database,
                DbUser=self.user,
                Sql=sql,
                StatementName=f"init-{int(datetime.now().timestamp())}",
            )

            statement_id = response["Id"]

            # Wait for completion
            while True:
                status_response = self.client.describe_statement(Id=statement_id)
                status = status_response["Status"]

                if status == "FINISHED":
                    logger.info(f"✓ Completed: {description}")
                    return True
                if status in ["FAILED", "ABORTED"]:
                    error = status_response.get("Error", "Unknown error")
                    logger.error(f"✗ Failed: {description} - {error}")
                    return False

                # Still running, wait a bit
                import time

                time.sleep(1)

        except Exception as e:
            logger.error(f"✗ Exception in {description}: {e!s}")
            return False

    def create_dimension_tables(self) -> bool:
        """Create all dimension tables with proper constraints."""
        # Tenant dimension table
        tenant_sql = """
        CREATE TABLE IF NOT EXISTS dim_tenant (
            tenant_key BIGINT IDENTITY(1,1) PRIMARY KEY,
            tenant_name VARCHAR(100) NOT NULL UNIQUE,
            timezone_name VARCHAR(50) DEFAULT 'UTC',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """

        # Agent dimension table with SCD Type 2
        agent_sql = """
        CREATE TABLE IF NOT EXISTS dim_agent (
            agent_key BIGINT IDENTITY(1,1) PRIMARY KEY,
            agent_name VARCHAR(100) NOT NULL,
            operator_id VARCHAR(50),
            agent_role VARCHAR(100),
            agent_uri VARCHAR(200),
            workstation VARCHAR(200),
            tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
            valid_from TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            valid_to TIMESTAMP,
            is_current BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """

        # Queue dimension table with SCD Type 2
        queue_sql = """
        CREATE TABLE IF NOT EXISTS dim_queue (
            queue_key BIGINT IDENTITY(1,1) PRIMARY KEY,
            ring_group_name VARCHAR(200) NOT NULL,
            ring_group_uri VARCHAR(500),
            tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
            valid_from TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            valid_to TIMESTAMP,
            is_current BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """

        # Date dimension table
        date_sql = """
        CREATE TABLE IF NOT EXISTS dim_date (
            date_key INTEGER PRIMARY KEY,
            full_date DATE,
            year INTEGER,
            quarter INTEGER,
            month INTEGER,
            day INTEGER,
            day_of_week INTEGER,
            day_name VARCHAR(10),
            month_name VARCHAR(10),
            is_weekend BOOLEAN
        );
        """

        # Time dimension table
        time_sql = """
        CREATE TABLE IF NOT EXISTS dim_time (
            time_key INTEGER PRIMARY KEY,
            hour INTEGER,
            minute INTEGER,
            second INTEGER,
            time_of_day VARCHAR(16),
            hour_name VARCHAR(10)
        );
        """

        tables = [
            (tenant_sql, "Create dim_tenant table"),
            (agent_sql, "Create dim_agent table"),
            (queue_sql, "Create dim_queue table"),
            (date_sql, "Create dim_date table"),
            (time_sql, "Create dim_time table"),
        ]

        success = True
        for sql, description in tables:
            if not self.execute_sql(sql, description):
                success = False

        return success

    def create_fact_tables(self) -> bool:
        """Create all fact tables with proper constraints and deduplication."""
        # Main fact table for agent events
        fact_event_sql = """
        CREATE TABLE IF NOT EXISTS fact_agent_event (
            state_key BIGINT IDENTITY(1,1) PRIMARY KEY,
            agent_key BIGINT REFERENCES dim_agent(agent_key),
            tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
            date_key INTEGER REFERENCES dim_date(date_key),
            time_key INTEGER REFERENCES dim_time(time_key),
            queue_key BIGINT REFERENCES dim_queue(queue_key),
            event_timestamp_utc TIMESTAMP NOT NULL,
            event_timestamp_local TIMESTAMP,
            event_type VARCHAR(50) NOT NULL,
            reason_code VARCHAR(100),
            busied_out_action VARCHAR(100),
            busied_out_duration INTEGER,
            media_label VARCHAR(200),
            workstation VARCHAR(200),
            device_name VARCHAR(50),
            event_data_json SUPER,
            processed_at_utc TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT unique_agent_event UNIQUE (agent_key, tenant_key, event_timestamp_utc, event_type, media_label)
        );
        """

        # Agent state intervals fact table
        fact_intervals_sql = """
        CREATE TABLE IF NOT EXISTS fact_agent_intervals (
            interval_key BIGINT IDENTITY(1,1) PRIMARY KEY,
            agent_key BIGINT REFERENCES dim_agent(agent_key),
            tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
            start_date_key INTEGER REFERENCES dim_date(date_key),
            start_time_key INTEGER REFERENCES dim_time(time_key),
            end_date_key INTEGER REFERENCES dim_date(date_key),
            end_time_key INTEGER REFERENCES dim_time(time_key),
            queue_key BIGINT REFERENCES dim_queue(queue_key),
            interval_start_utc TIMESTAMP NOT NULL,
            interval_end_utc TIMESTAMP,
            interval_start_local TIMESTAMP,
            interval_end_local TIMESTAMP,
            interval_duration_seconds INTEGER,
            state_type VARCHAR(50) NOT NULL,
            reason_code VARCHAR(100),
            is_current_interval BOOLEAN DEFAULT FALSE,
            processed_at_utc TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """

        # ACD sessions fact table
        fact_acd_sql = """
        CREATE TABLE IF NOT EXISTS fact_acd_session (
            session_key BIGINT IDENTITY(1,1) PRIMARY KEY,
            agent_key BIGINT REFERENCES dim_agent(agent_key),
            queue_key BIGINT REFERENCES dim_queue(queue_key),
            tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
            date_key INTEGER REFERENCES dim_date(date_key),
            login_time_key INTEGER REFERENCES dim_time(time_key),
            logout_time_key INTEGER REFERENCES dim_time(time_key),
            acd_login_utc TIMESTAMP NOT NULL,
            acd_logout_utc TIMESTAMP,
            acd_login_local TIMESTAMP,
            acd_logout_local TIMESTAMP,
            session_duration_seconds INTEGER,
            is_active_session BOOLEAN DEFAULT FALSE,
            processed_at_utc TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """

        tables = [
            (fact_event_sql, "Create fact_agent_event table"),
            (fact_intervals_sql, "Create fact_agent_intervals table"),
            (fact_acd_sql, "Create fact_acd_session table"),
        ]

        success = True
        for sql, description in tables:
            if not self.execute_sql(sql, description):
                success = False

        return success

    def create_indexes(self) -> bool:
        """Create performance indexes."""
        indexes = [  # Dimension table indexes
            (
                "CREATE INDEX IF NOT EXISTS idx_dim_agent_tenant_name ON dim_agent(tenant_key, agent_name, is_current);",
                "Create agent dimension index",
            ),
            (
                "CREATE INDEX IF NOT EXISTS idx_dim_queue_tenant_name ON dim_queue(tenant_key, ring_group_name, is_current);",
                "Create queue dimension index",
            ),
            # Fact table indexes
            (
                "CREATE INDEX IF NOT EXISTS idx_fact_agent_event_timestamp ON fact_agent_event(event_timestamp_utc);",
                "Create event timestamp index",
            ),
            (
                "CREATE INDEX IF NOT EXISTS idx_fact_agent_event_agent_time ON fact_agent_event(agent_key, event_timestamp_utc);",
                "Create agent-time index",
            ),
            (
                "CREATE INDEX IF NOT EXISTS idx_fact_agent_intervals_current ON fact_agent_intervals(agent_key, is_current_interval);",
                "Create intervals current index",
            ),
            (
                "CREATE INDEX IF NOT EXISTS idx_fact_acd_session_active ON fact_acd_session(agent_key, queue_key, is_active_session);",
                "Create ACD session active index",
            ),
        ]

        success = True
        for sql, description in indexes:
            if not self.execute_sql(sql, description):
                success = False

        return success

    def optimize_for_redshift(self) -> bool:
        """Apply Redshift-specific optimizations."""
        optimizations = [
            # Distribution styles for small dimension tables
            (
                "ALTER TABLE dim_tenant SET DISTSTYLE ALL;",
                "Set tenant distribution style",
            ),
            ("ALTER TABLE dim_date SET DISTSTYLE ALL;", "Set date distribution style"),
            ("ALTER TABLE dim_time SET DISTSTYLE ALL;", "Set time distribution style"),
            # Distribution keys for larger tables
            (
                "ALTER TABLE dim_agent SET DISTKEY(tenant_key);",
                "Set agent distribution key",
            ),
            (
                "ALTER TABLE dim_queue SET DISTKEY(tenant_key);",
                "Set queue distribution key",
            ),
            (
                "ALTER TABLE fact_agent_event SET DISTKEY(agent_key);",
                "Set event fact distribution key",
            ),
            (
                "ALTER TABLE fact_agent_intervals SET DISTKEY(agent_key);",
                "Set intervals fact distribution key",
            ),
            (
                "ALTER TABLE fact_acd_session SET DISTKEY(agent_key);",
                "Set ACD fact distribution key",
            ),
            # Sort keys for optimal query performance
            (
                "ALTER TABLE fact_agent_event SET SORTKEY(event_timestamp_utc, agent_key);",
                "Set event fact sort key",
            ),
            (
                "ALTER TABLE fact_agent_intervals SET SORTKEY(interval_start_utc, agent_key);",
                "Set intervals fact sort key",
            ),
            (
                "ALTER TABLE fact_acd_session SET SORTKEY(acd_login_utc, agent_key);",
                "Set ACD fact sort key",
            ),
        ]

        success = True
        for sql, description in optimizations:
            if not self.execute_sql(sql, description):
                # Optimizations are not critical, log but continue
                logger.warning(f"Optimization failed but continuing: {description}")

        return success

    def populate_initial_data(self) -> bool:
        """Populate initial reference data."""
        # Insert default tenants
        tenant_sql = """
        INSERT INTO dim_tenant (tenant_name, timezone_name)
        VALUES
            ('Brandon911', 'America/Winnipeg'),
            ('TestTenant', 'UTC');
        """

        # Note: Date and time dimensions would be populated separately
        # due to their size (10+ years of dates, 86400 time records)

        if not self.execute_sql(tenant_sql, "Insert initial tenant data"):
            return False

        return True

    def initialize_all(self) -> bool:
        """Run complete database initialization."""
        logger.info("Starting complete database initialization...")

        steps = [
            (self.create_dimension_tables, "Creating dimension tables"),
            (self.create_fact_tables, "Creating fact tables"),
            (self.create_indexes, "Creating indexes"),
            (self.optimize_for_redshift, "Applying Redshift optimizations"),
            (self.populate_initial_data, "Populating initial data"),
        ]

        for step_func, description in steps:
            logger.info(f"Step: {description}")
            if not step_func():
                logger.error(f"Failed at step: {description}")
                return False

        logger.info("✓ Database initialization completed successfully!")
        return True


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Initialize Agent Event Processor database"
    )
    parser.add_argument("--cluster-id", help="Redshift cluster identifier")
    parser.add_argument("--database", help="Database name")
    parser.add_argument("--user", help="Database user")
    parser.add_argument("--region", default="us-east-1", help="AWS region")

    args = parser.parse_args()

    # Get configuration from args or environment
    cluster_id = args.cluster_id or os.environ.get("DATABASE_CLUSTER_IDENTIFIER")
    database = args.database or os.environ.get("DATABASE_REDSHIFT_DATABASE")
    user = args.user or os.environ.get("DATABASE_REDSHIFT_USER")
    region = args.region or os.environ.get("AWS_REGION", "us-east-1")

    if not all([cluster_id, database, user]):
        logger.error(
            "Missing required parameters. Provide via args or environment variables:"
        )
        logger.error("  --cluster-id or DATABASE_CLUSTER_IDENTIFIER")
        logger.error("  --database or DATABASE_REDSHIFT_DATABASE")
        logger.error("  --user or DATABASE_REDSHIFT_USER")
        sys.exit(1)

    # Initialize database
    initializer = DatabaseInitializer(cluster_id, database, user, region)

    if initializer.initialize_all():
        logger.info("Database initialization completed successfully!")
        sys.exit(0)
    else:
        logger.error("Database initialization failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
